{"name": "tanstack-start-example-basic", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vinxi dev", "build": "vinxi build", "start": "vinxi start"}, "dependencies": {"@tanstack/react-router": "^1.120.3", "@tanstack/react-router-devtools": "^1.120.3", "@tanstack/react-start": "^1.120.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.6.0", "vinxi": "0.5.3"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4"}}